/* ===== MODERN SETTINGS 2025 ===== */
/* DESKTOP ONLY */

/* Alpine.js cloaking */
[x-cloak] { display: none !important; }

/* Hide mobile view on desktop */
.mobile-settings-app {
    display: none !important;
}

/* ===== TOGGLE SWITCH ===== */
.toggle-switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 34px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

input:checked + .toggle-slider {
    background-color: var(--primary);
}

input:checked + .toggle-slider:before {
    transform: translateX(26px);
}

.toggle-slider:hover {
    box-shadow: 0 0 0 8px rgba(22, 185, 16, 0.1);
}

:root {
    --primary: #16b910;
    --primary-light: #34d349;
    --primary-dark: #059669;
    --primary-bg: #d1fae5;
    --white: #ffffff;
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-500: #64748b;
    --gray-700: #334155;
    --gray-900: #0f172a;
    --font-family: 'Inter', system-ui, sans-serif;
    --space-2: 0.5rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1.5rem;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --transition: 250ms ease;
}

.settings-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--space-8) var(--space-6);
}

.settings-header {
    margin-bottom: var(--space-8);
}

.settings-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--space-2);
}

.settings-subtitle {
    font-size: 1rem;
    color: var(--gray-500);
}

.settings-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--space-6);
}

.settings-card {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
    overflow: hidden;
    transition: all var(--transition);
}

.settings-card:hover {
    box-shadow: var(--shadow-md);
}

.card-header {
    padding: var(--space-5) var(--space-6);
    border-bottom: 1px solid var(--gray-100);
    background-color: var(--gray-50);
}

.card-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-900);
}

.card-subtitle {
    font-size: 0.875rem;
    color: var(--gray-500);
    margin-top: var(--space-2);
}

.card-body {
    padding: var(--space-6);
}

/* Setting Item */
/* Setting Item */
.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--gray-100);
    padding: var(--space-5) 0;
}

.setting-item:last-of-type {
    border-bottom: none;
}

.setting-item:first-of-type {
    padding-top: 0;
}

.setting-item:last-of-type {
    padding-bottom: 0;
}

.setting-info .setting-name {
    font-size: 1rem;
    font-weight: 600;
    color: var(--gray-700);
}

.setting-info .setting-description {
    font-size: 0.875rem;
    color: var(--gray-500);
    margin-top: var(--space-2);
}

.setting-item-details {
    padding: var(--space-4);
    margin-top: var(--space-4);
    background-color: var(--gray-50);
    border-radius: var(--radius-lg);
}

/* Logo Upload */
.logo-upload-container {
    display: flex;
    align-items: center;
    gap: var(--space-4);
}

.logo-preview {
    position: relative;
}

.logo-image {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid var(--gray-200);
}

.logo-remove-btn {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #ef4444;
    color: white;
    border: 2px solid white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
}

.upload-label {
    width: 80px;
    height: 80px;
    border: 2px dashed var(--gray-300);
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.upload-label:hover {
    border-color: var(--primary);
    background-color: var(--primary-bg);
}

.upload-label i {
    font-size: 1.5rem;
    color: var(--gray-500);
}

.upload-label span {
    font-size: 0.8rem;
    color: var(--gray-500);
    margin-top: var(--space-2);
}

/* Form Elements */
.form-label {
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: var(--space-2);
    display: block;
}

.form-input {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-lg);
    background: var(--white);
}

.form-select {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-lg);
    background: var(--white);
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.75rem center;
    background-repeat: no-repeat;
    background-size: 1rem;
}

.form-textarea {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-lg);
    background: var(--white);
    min-height: 100px;
    resize: vertical;
}

/* Gateway Notice */
.gateway-notice {
    text-align: center;
    padding: var(--space-8) 0;
    color: var(--gray-500);
}

.gateway-notice i {
    font-size: 2rem;
    margin-bottom: var(--space-4);
    display: block;
}

.current-theme-preview {
    display: flex;
    align-items: center;
    gap: var(--space-4);
}

.current-theme-preview .theme-preview-image { 
    width: 120px; 
    border-radius: var(--radius-lg); 
    border: 1px solid var(--gray-200);
}

.current-theme-preview .theme-info h4 { 
    font-weight: 600; 
    color: var(--gray-900);
    margin-bottom: 4px;
}

.current-theme-preview .theme-info p { 
    font-size: 0.875rem; 
    color: var(--gray-500); 
}

/* Toggle Switch */
.toggle-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 26px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--gray-300);
    transition: var(--transition);
    border-radius: 34px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 20px;
    width: 20px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: var(--transition);
    border-radius: 50%;
}

input:checked + .toggle-slider {
    background-color: var(--primary);
}

input:checked + .toggle-slider:before {
    transform: translateX(24px);
}

/* Action Buttons */
.settings-actions {
    display: flex;
    justify-content: flex-end;
    gap: var(--space-4);
    margin-top: var(--space-8);
    padding-top: var(--space-6);
    border-top: 1px solid var(--gray-200);
}

.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1.5rem;
    border-radius: var(--radius-lg);
    font-weight: 600;
    font-size: 0.875rem;
    border: none;
    cursor: pointer;
    transition: var(--transition);
}

.btn-primary {
    background-color: var(--primary);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-dark);
}

.btn-outline {
    background-color: transparent;
    border: 1px solid var(--gray-300);
    color: var(--gray-700);
}

.btn-outline:hover {
    background-color: var(--gray-100);
}

.btn i {
    margin-right: 0.5rem;
}

/* Modal */
.modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background-color: var(--white);
    border-radius: var(--radius-xl);
    width: 100%;
    max-width: 1000px;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.modal-header {
    padding: var(--space-5) var(--space-6);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-900);
}

.modal-close {
    background: none;
    border: none;
    color: var(--gray-500);
    font-size: 1.5rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-body {
    padding: var(--space-6);
    overflow-y: auto;
    max-height: 70vh;
}

.modal-footer {
    padding: var(--space-5) var(--space-6);
    border-top: 1px solid var(--gray-200);
    display: flex;
    justify-content: flex-end;
    gap: var(--space-4);
}

/* Theme Marketplace */
.theme-filters {
    display: flex;
    flex-wrap: wrap;
    gap: var(--space-4);
    margin-bottom: var(--space-6);
    border-bottom: 1px solid var(--gray-200);
    padding-bottom: var(--space-4);
}

.theme-filters button {
    background: none;
    border: none;
    padding: var(--space-2) var(--space-4);
    cursor: pointer;
    border-radius: var(--radius-lg);
}

.theme-filters button.active {
    background-color: var(--primary-bg);
    color: var(--primary-dark);
    font-weight: 600;
}

.theme-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: var(--space-6);
}

.theme-card {
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    overflow: hidden;
    transition: var(--transition);
}

.theme-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-md);
}

.theme-card-image {
    position: relative;
    width: 100%;
    height: 150px;
    overflow: hidden;
}

.theme-card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.theme-card-body {
    padding: var(--space-4);
}

.theme-card h4 {
    font-weight: 600;
    margin-bottom: var(--space-2);
    color: var(--gray-900);
}

.theme-card p {
    font-size: 0.875rem;
    color: var(--gray-500);
    margin-bottom: var(--space-4);
}

.theme-actions {
    display: flex;
    justify-content: space-between;
    gap: var(--space-2);
}

.theme-preview-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr;
}

.theme-name {
    padding: 8px 12px;
    text-align: center;
    font-weight: 500;
    font-size: 14px;
}

.theme-buttons {
    display: flex;
    padding: 0 12px 12px;
    gap: 8px;
}

.theme-btn {
    flex: 1;
    padding: 6px 10px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
}

.theme-btn.apply {
    background-color: var(--primary);
    color: white;
}

.theme-btn.preview {
    background-color: transparent;
    border: 1px solid var(--primary);
    color: var(--primary);
}

.theme-btn:hover {
    opacity: 0.9;
    transform: translateY(-1px);
}

.theme-btn:active {
    transform: translateY(0);
}

.theme-sidebar {
    width: 30%;
    background-color: var(--theme-secondary, #4caf50);
}

.theme-content {
    width: 70%;
    background-color: #fff;
}

.theme-name {
    padding: 0.5rem;
    text-align: center;
    font-size: 0.9rem;
    font-weight: 500;
}

/* Button Outline */
.btn-outline {
    background-color: transparent;
    border: 1px solid var(--primary, #66bb6a);
    color: var(--primary, #66bb6a);
}

.btn-outline:hover {
    background-color: var(--primary, #66bb6a);
    color: white;
}

/* Switch Toggle */
.switch-toggle {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.switch-label {
    font-weight: 500;
    color: var(--text, #222);
}

.switch {
    position: relative;
    display: inline-block;
    width: 51px;
    height: 31px;
}

.switch input { 
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #e9e9eb;
    transition: .3s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 34px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 27px;
    width: 27px;
    left: 2px;
    bottom: 2px;
    background-color: white;
    transition: .3s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 50%;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

input:checked + .slider {
    background-color: var(--primary, #66bb6a);
}

input:checked + .slider:before {
    transform: translateX(20px);
}

/* Settings Actions Footer */
.settings-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem; /* 16px */
    margin-top: 2rem; /* 32px */
    padding: 1.5rem 2rem; /* 24px 32px */
    border-top: 1px solid var(--border, #e0e0e0);
    background: #fcfcfc;
}

.btn {
    padding: 0.75rem 1.5rem; /* 12px 24px */
    border-radius: 8px;
    font-weight: 600;
    font-size: 0.9rem;
    text-decoration: none;
    cursor: pointer;
    border: 1px solid transparent;
    transition: all 0.2s ease;
}

.btn-secondary {
    background-color: #f0f0f0;
    color: var(--text-muted, #666);
    border-color: #e0e0e0;
}

.btn-secondary:hover {
    background-color: #e5e5e5;
    border-color: #d5d5d5;
    color: var(--text, #222);
}

.btn-primary {
    background-color: var(--primary, #66bb6a);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-dark, #4caf50);
    box-shadow: 0 4px 12px rgba(102, 187, 106, 0.25);
    transform: translateY(-2px);
}

/* Step Indicator */
.step-indicator {
    display: flex;
    justify-content: space-between;
    padding: 1.5rem 2rem;
    border-bottom: 1px solid var(--border);
    background-color: #f9f9f9;
    position: relative;
}

/* Current Theme Display */
.current-theme-display {
    display: flex;
    align-items: stretch;
    border: 1px solid var(--border);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.current-theme-preview {
    flex: 0 0 60%;
    height: 250px;
    overflow: hidden;
}

.theme-preview-large {
    width: 100%;
    height: 100%;
    border-right: 1px solid var(--border);
    background-color: var(--background-color, #fff);
    display: flex;
    flex-direction: column;
}

.theme-header {
.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    cursor: pointer;
    z-index: 2;
    flex: 1;
    max-width: 120px;
}

.step-number {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: #fff;
    border: 2px solid var(--border);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 14px;
    margin-bottom: 8px;
    transition: all 0.3s ease;
}

.step.active .step-number {
    background-color: var(--primary);
    border-color: var(--primary);
    color: #fff;
}

.step.completed .step-number {
    background-color: var(--primary);
    border-color: var(--primary);
    color: #fff;
}

.step-title {
    font-size: 12px;
    font-weight: 500;
    color: var(--text-muted);
    text-align: center;
}

.step.active .step-title {
    color: var(--text);
    font-weight: 600;
}

.step.completed .step-title {
    color: var(--primary);
}

/* Step Navigation */
.step-navigation {
    display: flex;
    justify-content: space-between;
    padding: 1.5rem 2rem;
    border-top: 1px solid var(--border);
    background-color: #f9f9f9;
}

.step-content {
    padding: 2rem;
}

.step-form {
    display: none;
}

.step-form.active {
    display: block;
    animation: fadeIn 0.3s ease;
}

/* Theme Marketplace Modal */
.modal {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    z-index: 99999 !important; /* Máxima prioridad */
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    background-color: rgba(0, 0, 0, 0.7) !important; /* Fondo más oscuro para mayor contraste */
    overflow: auto !important;
}

.modal-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1;
}

.modal-container {
    width: 90% !important;
    max-width: 1100px !important;
    max-height: 85vh !important;
    background-color: #fff !important;
    border-radius: 8px !important;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.4) !important;
    position: relative !important;
    z-index: 2 !important;
    display: flex !important;
    flex-direction: column !important;
    margin: 30px auto !important;
}

@keyframes scaleIn {
    from { transform: scale(0.8); opacity: 0; }
    to { transform: scale(1); opacity: 1; }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
    color: var(--text);
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-muted);
    transition: color 0.2s ease;
}

.modal-close:hover {
    color: var(--text);
}

.modal-body {
    flex: 1;
    padding: 1.5rem;
    overflow-y: auto;
}

.modal-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--border);
    display: flex;
    justify-content: flex-end;
}

/* Theme Filter */
.theme-filter {
    margin-bottom: 1.5rem;
}

.theme-filter-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text);
}

.theme-filter-options {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.theme-filter-btn {
    border: 1px solid var(--gray-300);
    background-color: transparent;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.2s ease;
    color: var(--text-muted);
}

.theme-filter-btn.active {
    background-color: var(--primary);
    color: #fff;
    border-color: var(--primary);
}

.theme-filter-btn:hover:not(.active) {
    background-color: var(--gray-100);
    color: var(--gray-700);
}

/* Marketplace Theme Grid */
.marketplace-theme-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
}

.marketplace-theme-card {
    border: 1px solid var(--border);
    border-radius: 8px;
    overflow: hidden;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.marketplace-theme-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.marketplace-theme-preview {
    height: 180px;
    position: relative;
}

.marketplace-theme-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.marketplace-theme-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: #ff4081;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
}

.marketplace-theme-info {
    padding: 1rem;
    border-bottom: 1px solid var(--border);
}

.marketplace-theme-name {
    font-size: 1rem;
    font-weight: 600;
    margin: 0 0 0.5rem;
    color: var(--text);
}

.marketplace-theme-meta {
    display: flex;
    align-items: center;
    font-size: 0.875rem;
    color: var(--text-muted);
}

.marketplace-theme-category {
    text-transform: capitalize;
}

.marketplace-theme-separator {
    margin: 0 0.5rem;
}

.marketplace-theme-rating {
    display: flex;
    align-items: center;
}

.marketplace-theme-rating i {
    color: #ffc107;
    font-size: 0.875rem;
    margin-right: 0.25rem;
}

.marketplace-theme-buttons {
    display: flex;
    padding: 1rem;
    gap: 0.5rem;
}

.marketplace-theme-buttons .theme-btn {
    flex: 1;
    padding: 0.5rem;
    text-align: center;
    font-size: 0.875rem;
}

.transition {
    transition-property: opacity;
}

.duration-200 {
    transition-duration: 200ms;
}

.duration-300 {
    transition-duration: 300ms;
}

.ease-in {
    transition-timing-function: ease-in;
}

.ease-out {
    transition-timing-function: ease-out;
}

/* Alpine.js - Ocultar elementos hasta que Alpine los inicialice */
[x-cloak] {
    display: none !important;
}

