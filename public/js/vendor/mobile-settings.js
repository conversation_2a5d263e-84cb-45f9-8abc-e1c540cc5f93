/* ===== MOBILE SETTINGS INTERACTIONS ===== */

// Toast notification system for mobile
window.showToast = function(message, type = 'info') {
    // Remove existing toast
    const existingToast = document.querySelector('.mobile-toast');
    if (existingToast) {
        existingToast.remove();
    }
    
    // Create toast element
    const toast = document.createElement('div');
    toast.className = `mobile-toast mobile-toast-${type}`;
    toast.innerHTML = `
        <div class="mobile-toast-content">
            <i class="mobile-toast-icon fas ${getToastIcon(type)}"></i>
            <span class="mobile-toast-message">${message}</span>
        </div>
    `;
    
    // Add toast styles
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        left: 50%;
        transform: translateX(-50%);
        background: ${getToastColor(type)};
        color: white;
        padding: 12px 20px;
        border-radius: 12px;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
        z-index: 10000;
        font-size: 14px;
        font-weight: 500;
        max-width: 90%;
        animation: slideDown 0.3s ease-out;
    `;
    
    // Add animation keyframes
    if (!document.querySelector('#mobile-toast-styles')) {
        const style = document.createElement('style');
        style.id = 'mobile-toast-styles';
        style.textContent = `
            @keyframes slideDown {
                from {
                    opacity: 0;
                    transform: translateX(-50%) translateY(-20px);
                }
                to {
                    opacity: 1;
                    transform: translateX(-50%) translateY(0);
                }
            }
            .mobile-toast-content {
                display: flex;
                align-items: center;
                gap: 8px;
            }
        `;
        document.head.appendChild(style);
    }
    
    // Add to DOM
    document.body.appendChild(toast);
    
    // Auto remove after 3 seconds
    setTimeout(() => {
        if (toast.parentNode) {
            toast.style.animation = 'slideUp 0.3s ease-out forwards';
            setTimeout(() => toast.remove(), 300);
        }
    }, 3000);
    
    // Add haptic feedback
    window.mobileHaptic && window.mobileHaptic('light');
};

function getToastIcon(type) {
    const icons = {
        'success': 'fa-check-circle',
        'error': 'fa-exclamation-circle',
        'warning': 'fa-exclamation-triangle',
        'info': 'fa-info-circle'
    };
    return icons[type] || icons.info;
}

function getToastColor(type) {
    const colors = {
        'success': 'linear-gradient(135deg, #10b981 0%, #34d399 100%)',
        'error': 'linear-gradient(135deg, #ef4444 0%, #f87171 100%)',
        'warning': 'linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%)',
        'info': 'linear-gradient(135deg, #3b82f6 0%, #60a5fa 100%)'
    };
    return colors[type] || colors.info;
}

// Mobile haptic feedback
window.mobileHaptic = function(type = 'light') {
    if ('vibrate' in navigator) {
        const patterns = {
            'light': 10,
            'medium': 20,
            'heavy': 50
        };
        navigator.vibrate(patterns[type] || patterns.light);
    }
};

// Mobile swipe detection
window.addSwipeListener = function(element, callback) {
    let startX = 0;
    let startY = 0;
    let currentX = 0;
    let currentY = 0;
    
    element.addEventListener('touchstart', (e) => {
        startX = e.touches[0].clientX;
        startY = e.touches[0].clientY;
    }, { passive: true });
    
    element.addEventListener('touchmove', (e) => {
        currentX = e.touches[0].clientX;
        currentY = e.touches[0].clientY;
    }, { passive: true });
    
    element.addEventListener('touchend', (e) => {
        const diffX = startX - currentX;
        const diffY = startY - currentY;
        
        if (Math.abs(diffX) > Math.abs(diffY)) {
            if (Math.abs(diffX) > 50) {
                callback(diffX > 0 ? 'left' : 'right');
            }
        } else {
            if (Math.abs(diffY) > 50) {
                callback(diffY > 0 ? 'up' : 'down');
            }
        }
    }, { passive: true });
};

// Add ripple effect to buttons
function addRippleEffect(element, touch) {
    const rect = element.getBoundingClientRect();
    const ripple = document.createElement('span');
    const size = Math.max(rect.width, rect.height);
    const x = touch.clientX - rect.left - size / 2;
    const y = touch.clientY - rect.top - size / 2;
    
    ripple.style.cssText = `
        position: absolute;
        width: ${size}px;
        height: ${size}px;
        left: ${x}px;
        top: ${y}px;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        transform: scale(0);
        animation: ripple 0.6s linear;
        pointer-events: none;
    `;
    
    // Add ripple animation if not exists
    if (!document.querySelector('#ripple-styles')) {
        const style = document.createElement('style');
        style.id = 'ripple-styles';
        style.textContent = `
            @keyframes ripple {
                to {
                    transform: scale(4);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    }
    
    element.style.position = 'relative';
    element.style.overflow = 'hidden';
    element.appendChild(ripple);
    
    setTimeout(() => ripple.remove(), 600);
}

// Initialize mobile-specific features
document.addEventListener('DOMContentLoaded', function() {
    // Add touch-friendly interactions
    if (window.innerWidth <= 768) {
        // Add ripple effect to mobile buttons
        document.addEventListener('touchstart', function(e) {
            const target = e.target.closest('.mobile-btn, .mobile-filter-chip, .mobile-theme-btn, .mobile-stat-card');
            if (target) {
                addRippleEffect(target, e.touches[0]);
                window.mobileHaptic('light');
            }
        });
        
        // Add smooth scrolling for mobile
        document.documentElement.style.scrollBehavior = 'smooth';
        
        // Prevent zoom on double tap for form inputs
        document.addEventListener('touchend', function(e) {
            const target = e.target;
            if (target.matches('input, select, textarea')) {
                e.preventDefault();
                target.focus();
            }
        });
    }
});

// Mobile form validation helpers
window.mobileFormValidation = {
    validateRequired: function(value, fieldName) {
        if (!value || value.trim() === '') {
            window.showToast(`${fieldName} is required`, 'error');
            return false;
        }
        return true;
    },
    
    validateEmail: function(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            window.showToast('Please enter a valid email address', 'error');
            return false;
        }
        return true;
    },
    
    validatePhone: function(phone) {
        const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
        if (!phoneRegex.test(phone.replace(/\s/g, ''))) {
            window.showToast('Please enter a valid phone number', 'error');
            return false;
        }
        return true;
    }
};

// Mobile image upload helper
window.mobileImageUpload = {
    compressImage: function(file, maxWidth = 800, quality = 0.8) {
        return new Promise((resolve) => {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new Image();
            
            img.onload = function() {
                const ratio = Math.min(maxWidth / img.width, maxWidth / img.height);
                canvas.width = img.width * ratio;
                canvas.height = img.height * ratio;
                
                ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
                canvas.toBlob(resolve, 'image/jpeg', quality);
            };
            
            img.src = URL.createObjectURL(file);
        });
    },
    
    validateImageFile: function(file) {
        const validTypes = ['image/jpeg', 'image/png', 'image/webp'];
        const maxSize = 5 * 1024 * 1024; // 5MB
        
        if (!validTypes.includes(file.type)) {
            window.showToast('Please select a valid image file (JPEG, PNG, or WebP)', 'error');
            return false;
        }
        
        if (file.size > maxSize) {
            window.showToast('Image file size should be less than 5MB', 'error');
            return false;
        }
        
        return true;
    }
};
