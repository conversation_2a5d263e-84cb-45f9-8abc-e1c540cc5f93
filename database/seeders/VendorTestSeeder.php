<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Store;
use App\Models\Product;
use App\Models\Customer;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\ChatFlow;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;

class VendorTestSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::transaction(function () {
            // 1. Create a new Vendor User
            $vendor = User::create([
                'name' => 'Test Vendor',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'userType' => 'vendor',
                'isActive' => true,
            ]);

            $this->command->info('Test Vendor user created.');
            $this->command->info('Email: <EMAIL>');
            $this->command->info('Password: password');

            // 2. Create a Store for the new Vendor
            $store = Store::create([
                'user_id' => $vendor->id,
                'name' => 'Test Vendor\'s Awesome Store',
                'description' => 'A test store with some awesome products.',
                'storeUrl' => 'test-vendor-awesome-store',
                'businessCategory' => 'Electronics',
                'businessSubcategory' => 'Gadgets',
                'businessType' => 'product',
                'productType' => 'physical',
            ]);

            $this->command->info('Store created for Test Vendor.');

            // 3. Add Products to the Store
            $product1 = Product::create([
                'store_id' => $store->id,
                'name' => 'Test Product 1 - Smartwatch',
                'description' => 'A very smart watch for testing purposes.',
                'price' => 150.75,
                'stock' => 50,
            ]);

            $product2 = Product::create([
                'store_id' => $store->id,
                'name' => 'Test Product 2 - Drone',
                'description' => 'A flying drone for testing aerial photography.',
                'price' => 300.00,
                'stock' => 25,
            ]);

            $this->command->info('Added 2 products to the store.');

            // 4. Create Customers for the Store
            $customer1 = Customer::create([
                'store_id' => $store->id,
                'name' => 'John Doe',
                'email' => '<EMAIL>',
                'phone' => '1234567890',
            ]);

            $customer2 = Customer::create([
                'store_id' => $store->id,
                'name' => 'Jane Smith',
                'email' => '<EMAIL>',
                'phone' => '0987654321',
            ]);

            $this->command->info('Added 2 customers.');

            // 5. Create Orders and Order Items
            // Order 1 by John Doe
            $order1 = Order::create([
                'store_id' => $store->id,
                'customer_id' => $customer1->id,
                'total_amount' => $product1->price * 2,
                'status' => 'completed',
                'payment_status' => 'paid',
            ]);

            OrderItem::create([
                'order_id' => $order1->id,
                'product_id' => $product1->id,
                'quantity' => 2,
                'price' => $product1->price,
            ]);

            // Order 2 by Jane Smith
            $order2 = Order::create([
                'store_id' => $store->id,
                'customer_id' => $customer2->id,
                'total_amount' => ($product1->price * 1) + ($product2->price * 1),
                'status' => 'processing',
                'payment_status' => 'paid',
            ]);

            OrderItem::create([
                'order_id' => $order2->id,
                'product_id' => $product1->id,
                'quantity' => 1,
                'price' => $product1->price,
            ]);
            
            OrderItem::create([
                'order_id' => $order2->id,
                'product_id' => $product2->id,
                'quantity' => 1,
                'price' => $product2->price,
            ]);

            // Order 3 - Pending order for testing badge
            $order3 = Order::create([
                'store_id' => $store->id,
                'customer_id' => $customer1->id,
                'total_amount' => $product2->price,
                'status' => 'pending',
                'payment_status' => 'pending',
            ]);

            OrderItem::create([
                'order_id' => $order3->id,
                'product_id' => $product2->id,
                'quantity' => 1,
                'price' => $product2->price,
            ]);

            // Order 4 - Another pending order for testing badge
            $order4 = Order::create([
                'store_id' => $store->id,
                'customer_id' => $customer2->id,
                'total_amount' => $product1->price,
                'status' => 'pending',
                'payment_status' => 'pending',
            ]);

            OrderItem::create([
                'order_id' => $order4->id,
                'product_id' => $product1->id,
                'quantity' => 1,
                'price' => $product1->price,
            ]);

            $this->command->info('Added 4 orders with items (2 pending, 1 processing, 1 completed).');

            // 6. Create a Chat Flow
            ChatFlow::create([
                'store_id' => $store->id,
                'name' => 'Default Welcome Flow',
                'description' => 'The main chat flow for new customers.',
                'flow_data' => json_encode(['trigger' => 'welcome', 'steps' => ['message' => 'Welcome to our store!']]),
                'is_default' => true,
                'is_active' => true,
            ]);

            $this->command->info('Added a default chat flow.');
        });
    }
}
